//
//

#import <Cordova/CDV.h>
#import "CDVNavigator.h"

@implementation CDVNavigator

- (void)openUrl:(CDVInvokedUrlCommand *)command {
    NSString *url = [command.arguments objectAtIndex:0];
    [self openByUrl:url];
    
    CDVPluginResult *pluginResult = [CDVPluginResult resultWithStatus:CDVCommandStatus_OK];
    [self.commandDelegate sendPluginResult:pluginResult callbackId:command.callbackId];
}

- (void)openByUrl:(NSString *)url {
    NSURL *nsUrl = [NSURL URLWithString:url];
    if (!nsUrl) {
        NSLog(@"Invalid URL: %@", url);
        return;
    }
    
    if (@available(iOS 10.0, *)) {
        [[UIApplication sharedApplication] openURL:nsUrl options:@{} completionHandler:^(BOOL success) {
            if (!success) {
                NSLog(@"Failed to open URL: %@", url);
            }
        }];
    } else {
        // Fallback for older iOS versions
        BOOL success = [[UIApplication sharedApplication] openURL:nsUrl];
        if (!success) {
            NSLog(@"Failed to open URL: %@", url);
        }
    }
}

- (void)navigate:(CDVInvokedUrlCommand *)command {
    // Direct navigation without showing alert
    NSArray *latlng = command.arguments;
    NSString *toLat = [NSString stringWithFormat:@"%@",[latlng objectAtIndex:0]];
    NSString *toLng = [NSString stringWithFormat:@"%@",[latlng objectAtIndex:1]];
    
    // Check if Apple Maps is available
    BOOL isAppleMapsAvailable = [[UIApplication sharedApplication] canOpenURL:[NSURL URLWithString:@"maps://"]];
    
    // Check if Google Maps is available
    BOOL isGoogleMapsAvailable = [[UIApplication sharedApplication] canOpenURL:[NSURL URLWithString:@"comgooglemaps-x-callback://"]];
    
    // Check if Waze is available
    BOOL isWazeAvailable = [[UIApplication sharedApplication] canOpenURL:[NSURL URLWithString:@"waze://"]];
    
    // If no navigation apps are available, default to Apple Maps
    if (!isAppleMapsAvailable && !isGoogleMapsAvailable && !isWazeAvailable) {
        [self openByUrl:[NSString stringWithFormat:@"maps://?q=%f,%f", [toLat doubleValue], [toLng doubleValue]]];
        return;
    }
    
    // Otherwise show the chooser
    [self showAlert:latlng];
}

- (void)showAlert:(NSArray*) latlng {

    NSString *toLat = [NSString stringWithFormat:@"%@",[latlng objectAtIndex:0]];
    NSString *toLng = [NSString stringWithFormat:@"%@",[latlng objectAtIndex:1]];

    UIAlertController * alert = [UIAlertController
                                 alertControllerWithTitle:nil
                                 message:nil
                                 preferredStyle:UIAlertControllerStyleActionSheet];

    UIAlertAction* waze = [UIAlertAction
                           actionWithTitle:@"Waze"
                           style:UIAlertActionStyleDefault
                           handler:^(UIAlertAction * action) {
                               if ([[UIApplication sharedApplication] canOpenURL:[NSURL URLWithString:@"waze://"]]) {
                                   [self openByUrl:[NSString stringWithFormat:@"waze://?ll=%f,%f&navigate=yes", [toLat doubleValue], [toLng doubleValue]]];
                               } else {
                                   [self openByUrl:@"http://itunes.apple.com/us/app/id323229106"];
                               }
                           }];

    UIAlertAction* googleMaps = [UIAlertAction
                                 actionWithTitle:@"Google Maps"
                                 style:UIAlertActionStyleDefault
                                 handler:^(UIAlertAction * action) {
                                     if ([[UIApplication sharedApplication] canOpenURL:[NSURL URLWithString:@"comgooglemaps-x-callback://"]]) {
                                         [self openByUrl:[NSString stringWithFormat:@"comgooglemaps-x-callback://?daddr=%f,%f&x-success=sourceapp://?resume=true&x-source=NavigatorIntent",
                                                          [toLat doubleValue], [toLng doubleValue]]];
                                     } else {
                                         [self openByUrl:@"https://itunes.apple.com/us/app/google-maps-transit-food/id585027354?mt=8"];
                                     }
                                 }];

    UIAlertAction* appleMaps = [UIAlertAction
                                  actionWithTitle:@"Apple Maps"
                                  style:UIAlertActionStyleDefault
                                  handler:^(UIAlertAction * action) {
                                      if ([[UIApplication sharedApplication] canOpenURL:[NSURL URLWithString:@"maps://"]]) {
                                          [self openByUrl:[NSString stringWithFormat:@"maps://?q=%f,%f",
                                                           [toLat doubleValue], [toLng doubleValue]]];
                                      } else {
                                          [self openByUrl:@"https://itunes.apple.com/us/app/maps/id915056765?mt=8"];
                                      }
                                  }];

    UIAlertAction* cancel = [UIAlertAction
                             actionWithTitle:@"OK"
                             style:UIAlertActionStyleCancel
                             handler:^(UIAlertAction * action) {
                                 [alert dismissViewControllerAnimated:true completion:nil];
                             }];

    [alert addAction:waze];
    [alert addAction:googleMaps];
    [alert addAction:appleMaps];
    [alert addAction:cancel];

    // iPad presentation style
    if (UI_USER_INTERFACE_IDIOM() == UIUserInterfaceIdiomPad) {
        [alert setModalPresentationStyle:UIModalPresentationPopover];
        UIPopoverPresentationController *popPresenter = [alert popoverPresentationController];
        popPresenter.sourceView = self.webView.superview;
        [popPresenter setPermittedArrowDirections:0];
        popPresenter.sourceView = self.webView.superview;
        popPresenter.sourceRect = CGRectMake(CGRectGetMidX(self.webView.bounds), CGRectGetMidY(self.webView.bounds), 0, 0);
    }

    [self.viewController presentViewController:alert animated:YES completion:nil];
}

@end



